{"version": "0.2.0", "configurations": [{"name": "Debug NestJS App", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/@nestjs/cli/bin/nest.js", "args": ["start", "--debug", "--watch"], "env": {"NODE_ENV": "development", "PORT": "3001"}, "console": "integratedTerminal", "restart": true, "protocol": "inspector", "sourceMaps": true, "outFiles": ["${workspaceFolder}/dist/**/*.js"], "skipFiles": ["<node_internals>/**"], "runtimeArgs": ["--<PERSON><PERSON><PERSON>"], "envFile": "${workspaceFolder}/.env"}, {"name": "Debug NestJS App (Attach)", "type": "node", "request": "attach", "port": 9229, "restart": true, "localRoot": "${workspaceFolder}", "protocol": "inspector", "sourceMaps": true, "outFiles": ["${workspaceFolder}/dist/**/*.js"], "skipFiles": ["<node_internals>/**"]}, {"name": "Debug Jest Tests", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["--runInBand", "--no-cache", "--watchAll=false"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "disableOptimisticBPs": true, "windows": {"program": "${workspaceFolder}/node_modules/jest/bin/jest"}, "env": {"NODE_ENV": "test"}, "sourceMaps": true, "outFiles": ["${workspaceFolder}/dist/**/*.js"], "skipFiles": ["<node_internals>/**"]}, {"name": "Debug E2E Tests", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["--config", "./test/jest-e2e.json", "--runInBand", "--no-cache", "--watchAll=false"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "disableOptimisticBPs": true, "windows": {"program": "${workspaceFolder}/node_modules/jest/bin/jest"}, "env": {"NODE_ENV": "test"}, "sourceMaps": true, "outFiles": ["${workspaceFolder}/dist/**/*.js"], "skipFiles": ["<node_internals>/**"]}]}