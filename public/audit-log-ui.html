<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Audit Log Management</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 2rem; background: #f8f9fa; }
    h1 { color: #333; }
    table { border-collapse: collapse; width: 100%; margin-top: 1rem; background: #fff; box-shadow: 0 2px 8px #0001; }
    th, td { border: 1px solid #e0e0e0; padding: 10px 8px; text-align: left; }
    th { background: #f1f3f4; color: #222; font-weight: bold; }
    tr:nth-child(even) { background: #f9f9f9; }
    tr:hover { background: #e6f7ff; }
    button { padding: 4px 10px; border: none; background: #1976d2; color: #fff; border-radius: 3px; cursor: pointer; transition: background 0.2s; }
    button:hover:enabled { background: #1565c0; }
    button:disabled { background: #ccc; color: #888; cursor: not-allowed; }
    .filter { margin-bottom: 1rem; }
    .filter label { margin-right: 1rem; }
    .json-toggle { color: #1976d2; cursor: pointer; text-decoration: underline; background: none; border: none; font-size: 1em; padding: 0 8px; }
    .json-toggle:disabled { color: #aaa; text-decoration: none; cursor: not-allowed; }
    .json-data { max-width: 400px; overflow-x: auto; background: #f6f8fa; border-radius: 4px; padding: 4px 8px; font-family: 'Fira Mono', 'Consolas', monospace; font-size: 0.95em; }
    #pagination { margin-top: 1rem; }
    #pagination button { margin-right: 2px; background: #eee; color: #333; border: 1px solid #ccc; border-radius: 2px; }
    #pagination button[style*="font-weight:bold"] { background: #1976d2; color: #fff; }
    /* Modal styles */
    .modal-bg {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0; top: 0; width: 100vw; height: 100vh;
      background: rgba(0,0,0,0.3);
      align-items: center; justify-content: center;
    }
    .modal-bg.active { display: flex; }
    .modal {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 4px 24px #0003;
      max-width: 600px;
      width: 90vw;
      max-height: 80vh;
      padding: 1.5rem;
      position: relative;
      display: flex;
      flex-direction: column;
    }
    .modal pre {
      background: #f6f8fa;
      border-radius: 4px;
      padding: 1rem;
      font-family: 'Fira Mono', 'Consolas', monospace;
      font-size: 1em;
      overflow-x: auto;
      margin: 0;
      flex: 1;
    }
    .modal .close-btn {
      position: absolute;
      top: 10px; right: 16px;
      background: transparent;
      color: #888;
      border: none;
      border-radius: 3px;
      font-size: 1.5em;
      padding: 2px 10px;
      cursor: pointer;
      line-height: 1;
    }
    .modal .close-btn:hover { color: #e53935; background: #f5f5f5; }
  </style>
</head>
<body>
  <h1>Audit Log Management</h1>
  <div class="filter">
    <label>User ID: <input type="text" id="userId" /></label>
    <label>Action: <input type="text" id="action" /></label>
    <button onclick="loadLogs()">Filter</button>
  </div>
  <table id="logTable">
    <thead>
      <tr>
        <th>ID</th>
        <th>User ID</th>
        <th>Action</th>
        <th>IP</th>
        <th>User Agent</th>
        <th>Data</th>
        <th>Created At</th>
        <th>Delete</th>
      </tr>
    </thead>
    <tbody></tbody>
  </table>
  <div id="pagination"></div>
  <!-- Modal for JSON view -->
  <div class="modal-bg" id="jsonModalBg">
    <div class="modal">
      <button class="close-btn" onclick="closeJsonModal()" title="Đóng">&times;</button>
      <pre id="jsonModalContent"></pre>
    </div>
  </div>
  <script src="https://unpkg.com/dayjs@1.11.10/dayjs.min.js"></script>
  <script src="https://unpkg.com/dayjs@1.11.10/plugin/utc.js"></script>
  <script src="https://unpkg.com/dayjs@1.11.10/plugin/timezone.js"></script>
  <script>
    dayjs.extend(dayjs_plugin_utc);
    dayjs.extend(dayjs_plugin_timezone);
    let page = 1;
    let limit = 15;
    function showJsonModal(json) {
      document.getElementById('jsonModalContent').textContent = JSON.stringify(json, null, 2);
      document.getElementById('jsonModalBg').classList.add('active');
    }
    function closeJsonModal() {
      document.getElementById('jsonModalBg').classList.remove('active');
    }
    function isEmptyData(data) {
      return data === null || data === undefined || (typeof data === 'object' && Object.keys(data).length === 0);
    }
    function createJsonCell(data, rowIdx) {
      const container = document.createElement('div');
      container.className = 'json-data';
      const btn = document.createElement('button');
      btn.className = 'json-toggle';
      btn.textContent = 'Xem';
      if (isEmptyData(data)) {
        btn.disabled = true;
        btn.title = 'Không có dữ liệu';
      } else {
        btn.onclick = function() {
          showJsonModal(data);
        };
      }
      container.appendChild(btn);
      return container;
    }
    async function loadLogs(p = 1) {
      page = p;
      const userId = document.getElementById('userId').value;
      const action = document.getElementById('action').value;
      let url = `/sso/api/audit-logs?limit=${limit}&page=${page}`;
      if (userId) url += `&userId=${encodeURIComponent(userId)}`;
      if (action) url += `&action=${encodeURIComponent(action)}`;
      const res = await fetch(url, { headers: { 'Authorization': localStorage.getItem('jwt') || '' } });
      const {data} = await res.json();
      const tbody = document.querySelector('#logTable tbody');
      tbody.innerHTML = '';
      data.items.forEach((log, idx) => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
          <td>${log.id}</td>
          <td>${log.userId || ''}</td>
          <td>${log.action}</td>
          <td>${log.ip || ''}</td>
          <td>${log.userAgent || ''}</td>
          <td></td>
          <td>${dayjs.utc(log.createdAt).tz('Asia/Ho_Chi_Minh').format('DD/MM/YYYY HH:mm:ss')}</td>
          <td><button onclick="deleteLog('${log.id}')">Delete</button></td>
        `;
        // Data column: show JSON in modal or disable if empty
        tr.children[5].appendChild(createJsonCell(log.data, idx));
        tbody.appendChild(tr);
      });
      renderPagination(data.page, data.limit, data.total);
    }
    async function deleteLog(id) {
      if (!confirm('Delete this log?')) return;
      await fetch(`/sso/api/audit-logs/${id}`, { method: 'DELETE', headers: { 'Authorization': localStorage.getItem('jwt') || '' } });
      loadLogs(page);
    }
    function renderPagination(page, limit, total) {
      const div = document.getElementById('pagination');
      const pageCount = Math.ceil(total / limit);
      let html = '';
      for (let i = 1; i <= pageCount; i++) {
        html += `<button onclick=\"loadLogs(${i})\"${i === page ? ' style=\\"font-weight:bold\\"' : ''}>${i}</button> `;
      }
      div.innerHTML = html;
    }
    // JWT nhập tay cho demo
    if (!localStorage.getItem('jwt')) {
      const jwt = prompt('Nhập JWT token để truy cập log:');
      if (jwt) localStorage.setItem('jwt', 'Bearer ' + jwt);
    }
    loadLogs();
  </script>
</body>
</html> 