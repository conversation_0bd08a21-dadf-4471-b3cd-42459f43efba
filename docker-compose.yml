version: '3.8'
services:
  db:
    image: postgres:15
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: sso_db
    ports:
      - '5432:5432'
    volumes:
      - db_data:/var/lib/postgresql/data
  sso-server:
    build: .
    depends_on:
      - db
    ports:
      - '3000:3000'
    environment:
      DB_HOST: db
      DB_PORT: 5432
      DB_USER: postgres
      DB_PASS: postgres
      DB_NAME: sso_db
      JWT_SECRET: supersecretkey
      JWT_EXPIRES_IN: 3600s
      REFRESH_TOKEN_EXPIRES_IN: 7d
    volumes:
      - .:/app
    command: npm run start:dev
volumes:
  db_data:
