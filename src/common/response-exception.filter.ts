import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { ErrorCode } from './error-code';

@Catch()
export class ResponseExceptionFilter implements ExceptionFilter {
  catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    let status =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.OK;

    let errorCode = ErrorCode.SYSTEM_ERROR;
    let message = exception.message || 'Internal server error';

    // Nếu là lỗi validate (BadRequestException), lấy message chi tiết
    if (
      exception.response &&
      exception.response.message &&
      Array.isArray(exception.response.message)
    ) {
      message = exception.response.message.join('; ');
      errorCode = ErrorCode.VALIDATION_ERROR;
      status = HttpStatus.OK;
    }

    // Nếu có custom errorCode trong exception, lấy ra
    if (exception.response && exception.response.errorCode) {
      errorCode = exception.response.errorCode;
      // Nếu là lỗi auth thì trả về 401, còn lại 200
      if (
        errorCode === ErrorCode.AUTH_FAILED ||
        errorCode === ErrorCode.TOKEN_EXPIRED ||
        status === 401
      ) {
        status = HttpStatus.UNAUTHORIZED;
      } else {
        status = HttpStatus.OK;
      }
    }

    response.status(status).json({
      errorCode,
      message,
      data: null,
      timestamp: new Date().toISOString(),
    });
  }
}
