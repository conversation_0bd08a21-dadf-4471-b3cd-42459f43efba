import { Expose } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class UserSafeDto {
  @ApiProperty()
  @Expose()
  id: string;

  @ApiProperty()
  @Expose()
  email: string;

  @ApiProperty()
  @Expose()
  phone: string;

  @ApiProperty({ required: false })
  @Expose()
  name?: string;

  @ApiProperty({ required: false })
  @Expose()
  address?: string;

  @ApiProperty({ required: false })
  @Expose()
  birthDate?: Date;

  @ApiProperty()
  @Expose()
  deletedAt?: Date;

  @ApiProperty()
  @Expose()
  createdAt: Date;

  @ApiProperty()
  @Expose()
  updatedAt: Date;
}
