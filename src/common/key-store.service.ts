import { Injectable } from '@nestjs/common';

type AudienceKeyConfig = {
  kid: string;
  privateKey: string;
};

@Injectable()
export class KeyStoreService {
  private readonly audienceKeys: Record<string, AudienceKeyConfig> = {};

  constructor() {
    const raw = process.env.JWT_AUDIENCE_KEYS_JSON;
    if (raw) {
      try {
        const parsed = JSON.parse(raw) as Record<string, AudienceKeyConfig>;
        this.audienceKeys = parsed || {};
      } catch (error) {
        this.audienceKeys = {} as Record<string, AudienceKeyConfig>;
      }
    }
  }

  getSigningKeyForAudience(aud?: string): AudienceKeyConfig | undefined {
    if (!aud) return undefined;
    return this.audienceKeys[aud];
  }
}
