export enum AuditAction {
  // Authentication actions
  LOGIN = 'login',
  LOGOUT = 'logout',
  REGISTER = 'register',
  REFRESH_TOKEN = 'refresh_token',

  // User management actions
  USER_CREATE = 'user_create',
  USER_UPDATE_INFO = 'user_update_info',
  USER_DELETE = 'user_delete',
  USER_CHANGE_PASSWORD = 'user_change_password',

  // Address management actions
  ADDRESS_CREATE = 'address_create',
  ADDRESS_UPDATE = 'address_update',
  ADDRESS_DELETE = 'address_delete',

  // OTP actions
  OTP_GENERATE = 'otp_generate',
  OTP_VERIFY_SUCCESS = 'otp_verify_success',
  OTP_VERIFY_FAIL = 'otp_verify_fail',

  // Future actions (for extensibility)
  SESSION_CREATE = 'session_create',
  SESSION_REVOKE = 'session_revoke',
  PASSWORD_RESET_REQUEST = 'password_reset_request',
  PASSWORD_RESET_SUCCESS = 'password_reset_success',
  ACCOUNT_LOCK = 'account_lock',
  ACCOUNT_UNLOCK = 'account_unlock',
  PROFILE_VIEW = 'profile_view',
  SETTINGS_UPDATE = 'settings_update',
}
