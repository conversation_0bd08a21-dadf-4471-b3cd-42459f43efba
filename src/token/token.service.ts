import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Token } from '../entities/token.entity';

@Injectable()
export class TokenService {
  constructor(
    @InjectRepository(Token)
    private readonly tokenRepository: Repository<Token>,
  ) {}

  async createToken(data: Partial<Token>): Promise<Token> {
    const token = this.tokenRepository.create(data);
    return this.tokenRepository.save(token);
  }

  async findByAccessToken(token: string): Promise<Token | undefined> {
    const found = await this.tokenRepository.findOne({
      where: { accessToken: token },
    });
    return found ?? undefined;
  }

  async findByRefreshToken(token: string): Promise<Token | undefined> {
    const found = await this.tokenRepository.findOne({
      where: { refreshToken: token },
    });
    return found ?? undefined;
  }
}
