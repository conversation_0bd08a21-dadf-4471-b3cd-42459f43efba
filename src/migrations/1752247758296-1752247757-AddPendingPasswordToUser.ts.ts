import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPendingPasswordToUser1752247758296
  implements MigrationInterface
{
  name = 'AddPendingPasswordToUser1752247758296';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE `users` ADD `pendingPassword` varchar(255) NULL',
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      'ALTER TABLE `users` DROP COLUMN `pendingPassword`',
    );
  }
}
