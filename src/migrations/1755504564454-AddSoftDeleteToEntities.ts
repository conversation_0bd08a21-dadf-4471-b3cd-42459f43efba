import { MigrationInterface, QueryRunner } from "typeorm";

export class AddSoftDeleteToEntities1755504564454 implements MigrationInterface {
    name = 'AddSoftDeleteToEntities1755504564454'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`users\` ADD \`deletedAt\` datetime(6) NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`deletedAt\``);
    }

}
