import { MigrationInterface, QueryRunner } from "typeorm";

export class InitMigration1751630742350 implements MigrationInterface {
    name = 'InitMigration1751630742350'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE \`addresses\` (
                \`id\` varchar(36) NOT NULL,
                \`city\` varchar(255) NOT NULL,
                \`ward\` varchar(255) NOT NULL,
                \`detail\` varchar(255) NOT NULL,
                \`recipientName\` varchar(255) NOT NULL,
                \`recipientPhone\` varchar(255) NOT NULL,
                \`userId\` varchar(36) NULL,
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
        await queryRunner.query(`
            CREATE TABLE \`users\` (
                \`id\` varchar(36) NOT NULL,
                \`email\` varchar(255) NULL,
                \`phone\` varchar(255) NULL,
                \`password\` varchar(255) NOT NULL,
                \`name\` varchar(255) NULL,
                \`isPhoneVerified\` tinyint NOT NULL DEFAULT 0,
                \`birthDate\` date NULL,
                \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                UNIQUE INDEX \`IDX_97672ac88f789774dd47f7c8be\` (\`email\`),
                UNIQUE INDEX \`IDX_a000cca60bcf04454e72769949\` (\`phone\`),
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
        await queryRunner.query(`
            CREATE TABLE \`sessions\` (
                \`id\` varchar(36) NOT NULL,
                \`userId\` varchar(255) NOT NULL,
                \`refreshToken\` varchar(255) NOT NULL,
                \`userAgent\` varchar(255) NULL,
                \`ip\` varchar(255) NULL,
                \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                \`revokedAt\` datetime NULL,
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
        await queryRunner.query(`
            CREATE TABLE \`tokens\` (
                \`id\` varchar(36) NOT NULL,
                \`userId\` varchar(255) NOT NULL,
                \`clientId\` varchar(255) NOT NULL,
                \`accessToken\` varchar(255) NOT NULL,
                \`refreshToken\` varchar(255) NOT NULL,
                \`expiresAt\` datetime NOT NULL,
                \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
        await queryRunner.query(`
            CREATE TABLE \`otps\` (
                \`id\` varchar(36) NOT NULL,
                \`userId\` varchar(255) NULL,
                \`phone\` varchar(255) NULL,
                \`otp\` varchar(255) NOT NULL,
                \`type\` varchar(255) NOT NULL,
                \`expiresAt\` timestamp NOT NULL,
                \`usedAt\` timestamp NULL,
                \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                \`ip\` varchar(255) NULL,
                \`attempts\` int NOT NULL DEFAULT '0',
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
        await queryRunner.query(`
            CREATE TABLE \`clients\` (
                \`id\` varchar(36) NOT NULL,
                \`clientId\` varchar(255) NOT NULL,
                \`clientSecret\` varchar(255) NOT NULL,
                \`redirectUris\` text NOT NULL,
                \`name\` varchar(255) NOT NULL,
                \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                UNIQUE INDEX \`IDX_c8526f623c0beed53b60cb31bf\` (\`clientId\`),
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
        await queryRunner.query(`
            CREATE TABLE \`audit_logs\` (
                \`id\` varchar(36) NOT NULL,
                \`userId\` varchar(255) NULL,
                \`clientId\` varchar(255) NULL,
                \`action\` varchar(255) NOT NULL,
                \`ip\` varchar(255) NULL,
                \`userAgent\` varchar(255) NULL,
                \`data\` json NULL,
                \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                PRIMARY KEY (\`id\`)
            ) ENGINE = InnoDB
        `);
        await queryRunner.query(`
            ALTER TABLE \`addresses\`
            ADD CONSTRAINT \`FK_95c93a584de49f0b0e13f753630\` FOREIGN KEY (\`userId\`) REFERENCES \`users\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE \`addresses\` DROP FOREIGN KEY \`FK_95c93a584de49f0b0e13f753630\`
        `);
        await queryRunner.query(`
            DROP TABLE \`audit_logs\`
        `);
        await queryRunner.query(`
            DROP INDEX \`IDX_c8526f623c0beed53b60cb31bf\` ON \`clients\`
        `);
        await queryRunner.query(`
            DROP TABLE \`clients\`
        `);
        await queryRunner.query(`
            DROP TABLE \`otps\`
        `);
        await queryRunner.query(`
            DROP TABLE \`tokens\`
        `);
        await queryRunner.query(`
            DROP TABLE \`sessions\`
        `);
        await queryRunner.query(`
            DROP INDEX \`IDX_a000cca60bcf04454e72769949\` ON \`users\`
        `);
        await queryRunner.query(`
            DROP INDEX \`IDX_97672ac88f789774dd47f7c8be\` ON \`users\`
        `);
        await queryRunner.query(`
            DROP TABLE \`users\`
        `);
        await queryRunner.query(`
            DROP TABLE \`addresses\`
        `);
    }

}
