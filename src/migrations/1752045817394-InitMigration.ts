import { MigrationInterface, QueryRunner } from "typeorm";

export class InitMigration1752045817394 implements MigrationInterface {
    name = 'InitMigration1752045817394'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`users\` ADD \`address\` varchar(255) NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`address\``);
    }

}
