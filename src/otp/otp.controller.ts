import {
  Controller,
  Post,
  Body,
  Req,
  ValidationPipe,
  UsePipes,
} from '@nestjs/common';
import { ApiBody, ApiTags } from '@nestjs/swagger';
import { OtpService } from './otp.service';
import { Request } from 'express';
import { SendOtpDto, VerifyOtpDto } from './otp.dto';

@ApiTags('otp')
@Controller('otp')
export class OtpController {
  constructor(private readonly otpService: OtpService) {}

  @Post('send')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  @ApiBody({ type: SendOtpDto })
  async sendPhoneOtp(@Body() body: SendOtpDto, @Req() req: Request) {
    const audience = req.headers['x-audience'] as string;
    return this.otpService.sendOtp(
      body.phone,
      body.type,
      req.ip,
      req.headers['user-agent'],
      audience,
    );
  }

  @Post('verify')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  @ApiBody({ type: VerifyOtpDto })
  async verifyPhoneOtp(@Body() body: VerifyOtpDto, @Req() req: Request) {
    const audience = req.headers['x-audience'] as string;
    return this.otpService.verifyOtp(
      body.phone,
      body.otp,
      body.type,
      req.ip,
      req.headers['user-agent'],
      audience,
    );
  }
}
