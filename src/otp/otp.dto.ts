import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';
import { OtpType } from '../entities/otp.entity';

export class SendOtpDto {
  @ApiProperty({ example: '**********' })
  @IsNotEmpty()
  @IsString()
  phone: string;

  @ApiProperty({
    example: 'phone_verification',
    enum: [
      'phone_verification',
      'email',
      'forgot_password',
      'change_password',
      'delete_account',
    ],
  })
  @IsNotEmpty()
  @IsString()
  type: OtpType;
}

export class VerifyOtpDto {
  @ApiProperty({ example: '**********' })
  @IsNotEmpty()
  @IsString()
  phone: string;

  @ApiProperty({ example: '1234' })
  @IsNotEmpty()
  @IsString()
  otp: string;

  @ApiProperty({
    example: 'phone_verification',
    enum: [
      'phone_verification',
      'email',
      'forgot_password',
      'change_password',
      'delete_account',
    ],
  })
  @IsNotEmpty()
  @IsString()
  type: OtpType;
}

export class ESmsResponseDto {
  CodeResult!: string;
  CountRegenerate!: string;
  SMSID!: string;
}
