import { Modu<PERSON>, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Otp } from '../entities/otp.entity';
import { OtpService } from './otp.service';
import { AuditLogModule } from '../audit-log/audit-log.module';
import { OtpController } from './otp.controller';
import { JwtModule } from '@nestjs/jwt';
import { UserModule } from '../user/user.module';
import { HttpModule } from '@nestjs/axios';
import { KeyStoreService } from '../common/key-store.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([Otp]),
    AuditLogModule,
    JwtModule,
    forwardRef(() => UserModule),
    HttpModule,
  ],
  providers: [OtpService, KeyStoreService],
  exports: [OtpService],
  controllers: [OtpController],
})
export class OtpModule {}
