import { Injectable, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull, MoreThan } from 'typeorm';
import { Otp, OtpType } from '../entities/otp.entity';
import { v4 as uuidv4 } from 'uuid';
import { ErrorCode } from '../common/error-code';
import { AuditLogService } from '../audit-log/audit-log.service';
import { JwtService } from '@nestjs/jwt';
import { UserService } from '../user/user.service';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom, catchError } from 'rxjs';
import { AuditAction } from '../common/audit-action.enum';
import { KeyStoreService } from '../common/key-store.service';

@Injectable()
export class OtpService {
  private readonly logger = new Logger(OtpService.name);
  constructor(
    @InjectRepository(Otp)
    private readonly otpRepository: Repository<Otp>,
    private readonly auditLogService: AuditLogService,
    private readonly jwtService: JwtService,
    private readonly userService: UserService,
    private readonly httpService: HttpService,
    private readonly keyStore: KeyStoreService,
  ) {}

  formatPemKey(key: string): string {
    if (!key) return '';

    // First, convert literal \n strings to actual newlines
    const formattedKey = key.replace(/\\n/g, '\n');

    // Remove any existing headers/footers and clean up whitespace to get just the base64 content
    const cleanKey = formattedKey
      .replace(/-----BEGIN PRIVATE KEY-----/g, '')
      .replace(/-----END PRIVATE KEY-----/g, '')
      .replace(/\s+/g, '');

    // Validate that we have base64 content
    if (!cleanKey || cleanKey.length < 100) {
      throw new Error('Invalid private key: insufficient content');
    }

    // Split into 64-character lines
    const lines = cleanKey.match(/.{1,64}/g) || [];

    // Return properly formatted PEM key
    return `-----BEGIN PRIVATE KEY-----\n${lines.join('\n')}\n-----END PRIVATE KEY-----`;
  }

  async generateOtp(
    phone: string,
    type: OtpType,
    ip?: string,
    userAgent?: string,
  ): Promise<Otp> {
    const otpCode = Math.floor(1000 + Math.random() * 9000).toString();
    const expiresAt = new Date(Date.now() + 300 * 1000);
    const user = await this.userService.findByPhone(phone);
    if (!user) {
      throw new BadRequestException({
        errorCode: ErrorCode.USER_NOT_FOUND,
        message: 'Tài khoản không tồn tại',
      });
    }
    const otp = this.otpRepository.create({
      id: uuidv4(),
      userId: user.id,
      phone,
      otp: otpCode,
      type,
      expiresAt,
      ip,
      attempts: 0,
    });
    const savedOtp = await this.otpRepository.save(otp);
    await this.auditLogService.logAction({
      userId: user.id,
      action: AuditAction.OTP_GENERATE,
      ip,
      userAgent,
      data: { phone, type, expiresAt },
    });
    return savedOtp;
  }

  async verifyOtp(
    phone: string,
    otpCode: string,
    type: OtpType,
    ip?: string,
    userAgent?: string,
    audience?: string,
  ): Promise<any> {
    const otpRecord = await this.otpRepository.findOne({
      where: {
        phone: phone,
        type: type,
        usedAt: IsNull(),
        expiresAt: MoreThan(new Date()),
      },
    });

    if (!otpRecord) {
      await this.auditLogService.logAction({
        action: AuditAction.OTP_VERIFY_FAIL,
        ip,
        userAgent,
        data: { phone, type, reason: 'not_found_or_expired' },
      });
      throw new BadRequestException({
        errorCode: ErrorCode.INVALID_OTP,
        message: 'Mã xác thực không hợp lệ hoặc đã hết hạn',
      });
    }

    const user = await this.userService.findByPhone(phone);
    if (!user) {
      throw new BadRequestException({
        errorCode: ErrorCode.USER_NOT_FOUND,
        message: 'Tài khoản không tồn tại',
      });
    }

    if (otpRecord.otp !== otpCode) {
      otpRecord.attempts += 1;
      await this.otpRepository.save(otpRecord);
      if (otpRecord.attempts >= 5) {
        otpRecord.usedAt = new Date();
        await this.otpRepository.save(otpRecord);
      }
      await this.auditLogService.logAction({
        userId: user.id,
        action: AuditAction.OTP_VERIFY_FAIL,
        ip,
        userAgent,
        data: {
          phone,
          type,
          attempts: otpRecord.attempts,
          reason: 'wrong_code',
        },
      });
      throw new BadRequestException({
        errorCode: ErrorCode.INVALID_OTP,
        message: 'Mã xác thực không hợp lệ hoặc đã hết hạn',
      });
    }

    otpRecord.usedAt = new Date();
    await this.otpRepository.save(otpRecord);
    await this.auditLogService.logAction({
      userId: user.id,
      action: AuditAction.OTP_VERIFY_SUCCESS,
      ip,
      userAgent,
      data: { phone, type },
    });

    if (type === 'phone_verification') {
      if (user && !user.isPhoneVerified) {
        await this.userService.updateUser(user.id, { isPhoneVerified: true });
      }
      const payload = { sub: user.id, email: user.email };

      const signing = this.keyStore.getSigningKeyForAudience(audience);
      const accessToken = signing?.privateKey
        ? this.jwtService.sign(payload, {
            privateKey: this.formatPemKey(signing.privateKey),
            algorithm: 'RS256',
            keyid: signing.kid,
            expiresIn: process.env.JWT_EXPIRES_IN,
          })
        : this.jwtService.sign(payload, {
            secret: process.env.JWT_SECRET as string,
            expiresIn: process.env.JWT_EXPIRES_IN,
          });

      return { message: 'Xác thực thành công', accessToken };
    }

    if (type === 'delete_account') {
      await this.userService.deleteUser(user.id, ip, userAgent);
      return { message: 'Xóa tài khoản thành công' };
    }

    if (['forgot_password', 'change_password'].includes(type)) {
      // Try to get audience-specific signing key, fallback to HS256
      const signing = this.keyStore.getSigningKeyForAudience(audience);
      const resetToken = signing?.privateKey
        ? this.jwtService.sign(
            { sub: user.id },
            {
              privateKey: this.formatPemKey(signing.privateKey),
              algorithm: 'RS256',
              keyid: signing.kid,
              expiresIn: process.env.RESET_PASSWORD_EXPIRES_IN || '10m',
            },
          )
        : this.jwtService.sign(
            { sub: user.id },
            {
              secret: process.env.RESET_PASSWORD_SECRET,
              expiresIn: process.env.RESET_PASSWORD_EXPIRES_IN || '10m',
            },
          );
      return { resetToken };
    }
    return { message: 'Xác thực thành công' };
  }

  async invalidateOtps(phone: string, type: OtpType) {
    await this.otpRepository.update(
      { phone, type, usedAt: IsNull(), expiresAt: MoreThan(new Date()) },
      { usedAt: new Date() },
    );
  }

  async sendOtp(
    phone: string,
    type: OtpType,
    ip?: string,
    userAgent?: string,
    _audience?: string,
  ): Promise<{ message: string; otp: string }> {
    await this.invalidateOtps(phone, type);
    const otp = await this.generateOtp(phone, type, ip, userAgent);
    await this.sendSMS(phone, otp.otp);
    let message = 'Gửi mã xác thực thành công';
    if (type === 'forgot_password') {
      message = 'Gửi mã xác thực đặt lại mật khẩu thành công';
    }
    return {
      message,
      otp: otp.otp,
    };
  }

  async sendSMS(phone: string, otp: string) {
    const url = 'https://rest.esms.vn/MainService.svc/json/MultiChannelMessage';
    const body = {
      ApiKey: process.env.ESMS_API_KEY,
      SecretKey: process.env.ESMS_SECRET_KEY,
      Phone: phone,
      Channels: ['zalo', 'sms'],
      Data: [
        {
          TempID: '463293',
          Params: [otp],
          OAID: '1078627696114093747',
          Sandbox: '0',
          SendingMode: '1',
        },
        {
          Content: otp,
          IsUnicode: '0',
          SmsType: '2',
          Brandname: 'VOICE OTP',
          Sandbox: '0',
        },
      ],
    };
    const request = this.httpService.post(url, body, {});
    const { data } = await firstValueFrom(
      request.pipe(
        catchError((error) => {
          this.logger.error('Send SMS failed! ', error.message);
          throw new BadRequestException({
            code: ErrorCode.OPS_ERROR,
            message: 'Lỗi không xác định, vui lòng thử lại sau!',
          });
        }),
      ),
    );
    this.logger.log(`Send SMS to ${phone} with OTP ${otp} successfully!`);
    this.logger.debug(`Response from ESMS: ${JSON.stringify(data)}`);
  }
}
