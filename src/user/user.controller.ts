import {
  Controller,
  Get,
  Request,
  UseGuards,
  UnauthorizedException,
  Body,
  Patch,
  Delete,
  HttpCode,
  Post,
  Param,
} from '@nestjs/common';
import { UserService } from './user.service';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiParam } from '@nestjs/swagger';
import { plainToInstance } from 'class-transformer';
import { UserSafeDto } from '../common/user-safe.dto';
import {
  UpdateUserInfoDto,
  DeleteAccountDto,
  CreateAddressDto,
  UpdateAddressDto,
  ChangePasswordDto,
  RequestChangePasswordDto,
} from './user.dto';
import { OtpService } from '../otp/otp.service';
import { Address } from '../entities/address.entity';
import { ApiBody, ApiOperation } from '@nestjs/swagger';
import { ApiResponse } from '@nestjs/swagger';

@Controller('users')
export class UserController {
  constructor(
    private readonly userService: UserService,
    private readonly otpService: OtpService,
  ) {}

  @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard('jwt'))
  @Get('me')
  async getMe(@Request() req: { user: { sub: string } }): Promise<UserSafeDto> {
    const user = await this.userService.findById(req.user.sub);
    if (!user) throw new UnauthorizedException('User not found');
    return plainToInstance(UserSafeDto, user, {
      excludeExtraneousValues: true,
    });
  }

  @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard('jwt'))
  @Patch('me')
  async updateMe(
    @Request() req: any,
    @Body() body: UpdateUserInfoDto,
  ): Promise<UserSafeDto> {
    const ip =
      req.ip || req.headers['x-forwarded-for'] || req.connection?.remoteAddress;
    const userAgent = req.headers['user-agent'] || '';
    const result = await this.userService.updateUserInfo(
      req.user.sub,
      body,
      ip,
      userAgent,
    );

    return plainToInstance(UserSafeDto, result, {
      excludeExtraneousValues: true,
    });
  }

  @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard('jwt'))
  @Delete('me')
  @HttpCode(204)
  async deleteMe(
    @Request() req: any,
    @Body() body: DeleteAccountDto,
  ): Promise<void> {
    const user = await this.userService.findById(req.user.sub);
    if (!user) throw new UnauthorizedException('User not found');
    const ip =
      req.ip || req.headers['x-forwarded-for'] || req.connection?.remoteAddress;
    const userAgent = req.headers['user-agent'] || '';
    await this.otpService.verifyOtp(
      user.phone,
      body.otp,
      'delete_account',
      ip,
      userAgent,
    );
  }

  @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard('jwt'))
  @Get('me/addresses')
  async getMyAddresses(@Request() req: any): Promise<Address[]> {
    return this.userService.getAddresses(req.user.sub);
  }

  @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard('jwt'))
  @Post('me/addresses')
  async createMyAddress(@Request() req: any, @Body() body: CreateAddressDto) {
    const ip =
      req.ip || req.headers['x-forwarded-for'] || req.connection?.remoteAddress;
    const userAgent = req.headers['user-agent'] || '';
    return this.userService.createAddress(req.user.sub, body, ip, userAgent);
  }

  @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard('jwt'))
  @Patch('me/addresses/:id')
  @ApiParam({ name: 'id', type: String, description: 'Address ID' })
  async updateMyAddress(
    @Request() req: any,
    @Param('id') id: string,
    @Body() body: UpdateAddressDto,
  ) {
    const ip =
      req.ip || req.headers['x-forwarded-for'] || req.connection?.remoteAddress;
    const userAgent = req.headers['user-agent'] || '';
    return this.userService.updateAddress(
      req.user.sub,
      { ...body, id },
      ip,
      userAgent,
    );
  }

  @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard('jwt'))
  @Delete('me/addresses/:id')
  @ApiParam({ name: 'id', type: String, description: 'Address ID' })
  async deleteMyAddress(@Request() req: any, @Param('id') id: string) {
    const ip =
      req.ip || req.headers['x-forwarded-for'] || req.connection?.remoteAddress;
    const userAgent = req.headers['user-agent'] || '';
    return this.userService.deleteAddress(req.user.sub, id, ip, userAgent);
  }

  @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard('jwt'))
  @Post('me/request-change-password')
  @ApiOperation({
    summary: 'Request change password (send OTP to user)',
    description:
      'Yêu cầu đổi mật khẩu: nhập mật khẩu cũ và mật khẩu mới. Nếu mật khẩu cũ đúng, hệ thống sẽ gửi OTP về số điện thoại.',
  })
  @ApiBody({
    type: RequestChangePasswordDto,
    description:
      'oldPass là mật khẩu hiện tại, newPass là mật khẩu mới muốn đổi',
    examples: {
      example1: {
        summary: 'Yêu cầu đổi mật khẩu',
        value: {
          oldPass: 'oldPassword123',
          newPass: 'newPassword456',
        },
      },
    },
  })
  @ApiResponse({ status: 200, description: 'OTP đã được gửi về số điện thoại' })
  @ApiResponse({
    status: 400,
    description: 'Sai mật khẩu cũ hoặc user không tồn tại',
  })
  async requestChangePassword(
    @Request() req: any,
    @Body() body: RequestChangePasswordDto,
  ): Promise<{ message: string }> {
    const user = await this.userService.requestChangePasswordWithOldPass(
      req.user.sub,
      body.oldPass,
      body.newPass,
    );
    await this.otpService.sendOtp(user.phone, 'change_password');
    return { message: 'Mã xác thực đã được gửi đến số điện thoại của bạn' };
  }

  @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard('jwt'))
  @Post('me/change-password')
  @ApiOperation({ summary: 'Change password using reset token' })
  @ApiBody({
    type: ChangePasswordDto,
    description: 'Reset token lấy từ verify OTP, newPassword là mật khẩu mới',
    examples: {
      example1: {
        summary: 'Change password',
        value: {
          resetToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        },
      },
    },
  })
  @ApiResponse({ status: 204, description: 'Password changed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid or expired reset token' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async changePassword(
    @Request() req: any,
    @Body() body: ChangePasswordDto,
  ): Promise<void> {
    const ip =
      req.ip || req.headers['x-forwarded-for'] || req.connection?.remoteAddress;
    const userAgent = req.headers['user-agent'] || '';
    await this.userService.changePasswordWithToken(
      req.user.sub,
      body.resetToken,
      ip,
      userAgent,
    );
  }
}
