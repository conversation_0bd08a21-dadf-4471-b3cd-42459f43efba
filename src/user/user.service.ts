import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../entities/user.entity';
import * as bcrypt from 'bcrypt';
import { AuditLogService } from '../audit-log/audit-log.service';
import { Address } from '../entities/address.entity';
import {
  AddressDto,
  UpdateAddressDto,
  ResetPasswordTokenPayload,
} from './user.dto';
import { ErrorCode } from 'src/common/error-code';
import * as jwt from 'jsonwebtoken';
import { JwtService } from '@nestjs/jwt';
import { AuditAction } from '../common/audit-action.enum';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Address)
    private readonly addressRepository: Repository<Address>,
    private readonly auditLogService: AuditLogService,
    private readonly jwtService: JwtService,
  ) {}

  async findById(id: string): Promise<User | undefined> {
    const user = await this.userRepository.findOne({ where: { id } });
    return user ?? undefined;
  }

  async findByEmail(email: string): Promise<User | undefined> {
    const user = await this.userRepository.findOne({ where: { email } });
    return user ?? undefined;
  }

  async findByPhone(phone: string): Promise<User | undefined> {
    const user = await this.userRepository.findOne({ where: { phone } });
    return user ?? undefined;
  }

  async findByEmailOrPhone(identifier: string): Promise<User | undefined> {
    if (identifier.includes('@')) {
      return this.findByEmail(identifier);
    }
    return this.findByPhone(identifier);
  }

  async createUser(data: {
    email?: string;
    phone?: string;
    password: string;
    name?: string;
  }): Promise<User> {
    const hashed = await bcrypt.hash(data.password, 10);
    const user = this.userRepository.create({
      email: data.email,
      phone: data.phone,
      password: hashed,
      name: data.name,
    });
    const savedUser = await this.userRepository.save(user);
    await this.auditLogService.logAction({
      userId: savedUser.id,
      action: AuditAction.USER_CREATE,
      data: {
        email: savedUser.email,
        phone: savedUser.phone,
        name: savedUser.name,
      },
    });
    return savedUser;
  }

  async updateUser(id: string, data: Partial<User>): Promise<User> {
    if (data.password) {
      data.password = await bcrypt.hash(data.password, 10);
    }
    await this.userRepository.update(id, data);
    const updatedUser = await this.findById(id);
    return updatedUser!;
  }

  async updateUserInfo(
    id: string,
    data: {
      name?: string;
      birthDate?: string;
      email?: string;
      phone?: string;
      address?: string;
    },
    ip?: string,
    userAgent?: string,
  ): Promise<User> {
    const user = await this.findById(id);
    if (!user) {
      throw new BadRequestException({
        errorCode: ErrorCode.USER_NOT_FOUND,
        message: 'Tài khoản không tồn tại',
      });
    }
    const updateData: Partial<User> = {};
    if (data.name) updateData.name = data.name;
    if (data.birthDate) updateData.birthDate = new Date(data.birthDate);
    if (data.email) updateData.email = data.email;
    if (!user?.phone) updateData.phone = data.phone;
    if (data.address) updateData.address = data.address;
    await this.userRepository.update(id, updateData);
    const updatedUser = await this.findById(id);
    await this.auditLogService.logAction({
      userId: id,
      action: AuditAction.USER_UPDATE_INFO,
      data: updateData,
      ip,
      userAgent,
    });
    return updatedUser!;
  }

  async deleteUser(id: string, ip?: string, userAgent?: string): Promise<void> {
    await this.userRepository.softDelete(id);
    await this.auditLogService.logAction({
      userId: id,
      action: AuditAction.USER_DELETE,
      data: {},
      ip,
      userAgent,
    });
  }

  async getAddresses(userId: string): Promise<Address[]> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['addresses'],
    });
    return user?.addresses || [];
  }

  async updateAddresses(
    userId: string,
    addresses: { city: string; ward: string; detail: string }[],
    ip?: string,
    userAgent?: string,
  ): Promise<Address[]> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: ['addresses'],
    });
    if (!user) {
      throw new BadRequestException({
        errorCode: ErrorCode.USER_NOT_FOUND,
        message: 'Tài khoản không tồn tại',
      });
    }
    // Xoá toàn bộ address cũ khỏi DB
    await this.addressRepository.delete({ user: { id: userId } });
    // Tạo mới các address
    const newAddresses = addresses.map((addr) => {
      const a = new Address();
      a.city = addr.city;
      a.ward = addr.ward;
      a.detail = addr.detail;
      a.user = user;
      return a;
    });
    await this.addressRepository.save(newAddresses);
    await this.auditLogService.logAction({
      userId,
      action: AuditAction.ADDRESS_UPDATE,
      data: addresses,
      ip,
      userAgent,
    });
    return newAddresses;
  }

  async createAddress(
    userId: string,
    dto: AddressDto,
    ip?: string,
    userAgent?: string,
  ): Promise<Address> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new BadRequestException({
        errorCode: ErrorCode.USER_NOT_FOUND,
        message: 'Tài khoản không tồn tại',
      });
    }
    const address = new Address();
    address.city = dto.city;
    address.ward = dto.ward;
    address.detail = dto.detail;
    address.recipientName = dto.recipientName;
    address.recipientPhone = dto.recipientPhone;
    address.user = user;
    const saved = await this.addressRepository.save(address);
    await this.auditLogService.logAction({
      userId,
      action: AuditAction.ADDRESS_CREATE,
      data: dto,
      ip,
      userAgent,
    });
    return saved;
  }

  async updateAddress(
    userId: string,
    dto: UpdateAddressDto,
    ip?: string,
    userAgent?: string,
  ): Promise<Address> {
    const address = await this.addressRepository.findOne({
      where: { id: dto.id, user: { id: userId } },
    });
    if (!address) throw new Error('Address not found');
    address.city = dto.city;
    address.ward = dto.ward;
    address.detail = dto.detail;
    address.recipientName = dto.recipientName;
    address.recipientPhone = dto.recipientPhone;
    const saved = await this.addressRepository.save(address);
    await this.auditLogService.logAction({
      userId,
      action: AuditAction.ADDRESS_UPDATE,
      data: dto,
      ip,
      userAgent,
    });
    return saved;
  }

  async deleteAddress(
    userId: string,
    addressId: string,
    ip?: string,
    userAgent?: string,
  ): Promise<void> {
    const address = await this.addressRepository.findOne({
      where: { id: addressId, user: { id: userId } },
    });
    if (!address) throw new Error('Address not found');
    await this.addressRepository.delete(addressId);
    await this.auditLogService.logAction({
      userId,
      action: AuditAction.ADDRESS_DELETE,
      data: { id: addressId },
      ip,
      userAgent,
    });
  }

  async generateResetPasswordToken(userId: string): Promise<string> {
    return this.jwtService.sign(
      { sub: userId, issuedAt: Date.now() },
      {
        secret: process.env.RESET_PASSWORD_SECRET,
        expiresIn: process.env.RESET_PASSWORD_EXPIRES_IN || '10m',
      },
    );
  }

  async changePasswordWithToken(
    userId: string,
    resetToken: string,
    ip?: string,
    userAgent?: string,
  ): Promise<void> {
    try {
      jwt.verify(
        resetToken,
        process.env.RESET_PASSWORD_SECRET as string,
      ) as ResetPasswordTokenPayload;
    } catch {
      throw new BadRequestException({
        code: ErrorCode.TOKEN_EXPIRED,
        message: 'Mã xác thực không hợp lệ hoặc đã hết hạn',
      });
    }
    const user = await this.findById(userId);
    if (!user)
      throw new BadRequestException({
        code: ErrorCode.USER_NOT_FOUND,
        message: 'Tài khoản không tồn tại',
      });
    if (!user.pendingPassword) {
      throw new BadRequestException({
        code: ErrorCode.TOKEN_EXPIRED,
        message: 'Mã xác thực không hợp lệ hoặc đã hết hạn',
      });
    }

    await this.userRepository.update(userId, {
      password: user?.pendingPassword as string,
      pendingPassword: '',
    });

    await this.auditLogService.logAction({
      userId,
      action: AuditAction.USER_CHANGE_PASSWORD,
      data: { resetToken },
      ip,
      userAgent,
    });
  }

  async requestChangePasswordWithOldPass(
    userId: string,
    oldPass: string,
    newPass: string,
  ): Promise<User> {
    const user = await this.findById(userId);
    if (!user)
      throw new BadRequestException({
        code: ErrorCode.USER_NOT_FOUND,
        message: 'Tài khoản không tồn tại',
      });
    const isMatch = await bcrypt.compare(oldPass, user.password);
    if (!isMatch) {
      throw new BadRequestException({
        code: ErrorCode.INVALID_OLD_PASS,
        message: 'Mật khẩu hiện tại không đúng',
      });
    }
    // Hash và lưu newPass vào pendingPassword
    const hashed = await bcrypt.hash(newPass, 10);
    await this.userRepository.update(userId, { pendingPassword: hashed });

    return user;
  }
}
