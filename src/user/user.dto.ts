import {
  IsOptional,
  IsString,
  IsDateString,
  IsNotEmpty,
} from 'class-validator';
import { ApiPropertyOptional, ApiProperty } from '@nestjs/swagger';

export class UpdateUserInfoDto {
  @ApiPropertyOptional({ example: '<PERSON><PERSON><PERSON>' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ example: '1990-01-01' })
  @IsOptional()
  @IsDateString()
  birthDate?: string;

  @ApiPropertyOptional({ example: '<EMAIL>' })
  @IsOptional()
  @IsString()
  email?: string;

  @ApiPropertyOptional({ example: '+***********' })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiPropertyOptional({ example: '123 Đường ABC, Quận XYZ, TP. HCM' })
  @IsOptional()
  @IsString()
  address?: string;
}

export class DeleteAccountDto {
  @ApiProperty({ example: '123456' })
  @IsNotEmpty()
  otp: string;
}

export class AddressDto {
  @ApiPropertyOptional({ example: 'Hà Nội' })
  @IsString()
  city: string;

  @ApiPropertyOptional({ example: 'Phường Bách Khoa' })
  @IsString()
  ward: string;

  @ApiPropertyOptional({ example: 'Số 1 Đại Cồ Việt' })
  @IsString()
  detail: string;

  @ApiProperty({ example: 'Nguyễn Văn A' })
  @IsString()
  recipientName: string;

  @ApiProperty({ example: '**********' })
  @IsString()
  recipientPhone: string;
}

export class CreateAddressDto extends AddressDto {}

export class UpdateAddressDto extends AddressDto {
  @ApiPropertyOptional({ example: 'address-uuid' })
  @IsString()
  id: string;
}

export class DeleteAddressDto {
  @ApiPropertyOptional({ example: 'address-uuid' })
  @IsString()
  id: string;
}

export class RequestChangePasswordDto {
  @ApiProperty({ example: 'oldPassword123' })
  @IsNotEmpty()
  @IsString()
  oldPass: string;

  @ApiProperty({ example: 'newPassword456' })
  @IsNotEmpty()
  @IsString()
  newPass: string;
}

export class ChangePasswordDto {
  resetToken: string;
}

export class ResetPasswordTokenPayload {
  userId: string;
  issuedAt: number;
}
