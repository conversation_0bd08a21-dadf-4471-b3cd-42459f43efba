import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Param,
  Body,
  Query,
  Request,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { RegistrationApplicationService } from './registration-application.service';
import { 
  ReviewApplicationDto, 
  GetApplicationsQueryDto,
  ApplicationResponseDto 
} from './registration-application.dto';
import { plainToInstance } from 'class-transformer';
import { RoleGuard } from '../common/guards/role.guard';
import { Roles } from '../common/decorators/roles.decorator';
import { UserRole } from '../common/types';

@ApiTags('registration-applications')
@UseGuards(AuthGuard('jwt'), RoleGuard)
@Controller('registration-applications')
export class RegistrationApplicationController {
  constructor(
    private readonly applicationService: RegistrationApplicationService,
  ) {}

  @ApiBearerAuth('access-token')
  @Roles(UserRole.USER)
  @Get('my-applications')
  @ApiOperation({ summary: 'Get current user applications' })
  @ApiResponse({ 
    status: 200, 
    description: 'List of user applications',
    type: [ApplicationResponseDto]
  })
  async getMyApplications(@Request() req: any): Promise<ApplicationResponseDto[]> {
    const applications = await this.applicationService.getApplicationsByUser(req.user.sub);
    return applications.map(app => plainToInstance(ApplicationResponseDto, app));
  }

  @ApiBearerAuth('access-token')
  @Roles(UserRole.ADMIN)
  @Get(':id')
  @ApiOperation({ summary: 'Get application by ID' })
  @ApiResponse({ 
    status: 200, 
    description: 'Application details',
    type: ApplicationResponseDto
  })
  async getApplicationById(@Param('id') id: string): Promise<ApplicationResponseDto> {
    const application = await this.applicationService.getApplicationById(id);
    console.log(application)
    return plainToInstance(ApplicationResponseDto, application);
  }

  // Admin endpoints
  @ApiBearerAuth('access-token')
  @Roles(UserRole.ADMIN)
  @Get()
  @ApiOperation({ 
    summary: 'Get all applications (Admin only)',
    description: 'Get paginated list of all registration applications'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Paginated list of applications',
    schema: {
      type: 'object',
      properties: {
        applications: {
          type: 'array',
          items: { $ref: '#/components/schemas/ApplicationResponseDto' }
        },
        total: { type: 'number' },
        limit: { type: 'number' },
        offset: { type: 'number' }
      }
    }
  })
  async getAllApplications(
    @Query() query: GetApplicationsQueryDto,
  ): Promise<{
    applications: ApplicationResponseDto[];
    total: number;
    limit: number;
    offset: number;
  }> {
    const { applications, total } = await this.applicationService.getAllApplications(
      query.status,
      query.type,
      query.limit,
      query.offset,
    );

    return {
      applications: applications.map(app => plainToInstance(ApplicationResponseDto, app)),
      total,
      limit: query.limit || 10,
      offset: query.offset || 0,
    };
  }

  @ApiBearerAuth('access-token')
  @Roles(UserRole.ADMIN)
  @Patch(':id/review')
  @ApiOperation({ 
    summary: 'Review application (Admin only)',
    description: 'Approve or reject a registration application. Admin can review, users can cancel own applications.'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Application reviewed successfully',
    type: ApplicationResponseDto
  })
  async reviewApplication(
    @Param('id') id: string,
    @Body() reviewDto: ReviewApplicationDto,
    @Request() req: any,
  ): Promise<ApplicationResponseDto> {
    const ip = req.ip || req.headers['x-forwarded-for'] || req.connection?.remoteAddress;
    const userAgent = req.headers['user-agent'] || '';

    const reviewedBy = req.user.sub;
    
    const application = await this.applicationService.reviewApplication(
      id,
      reviewDto,
      reviewedBy,
      ip,
      userAgent,
    );

    return plainToInstance(ApplicationResponseDto, application);
  }
}
