import { Injectable, BadRequestException, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RegistrationApplication } from '../entities/registration-application.entity';
import { User } from '../entities/user.entity';
import { 
  RegistrationApplicationType, 
  RegistrationApplicationStatus,
  UserType,
  AuditAction 
} from '../common/types';
import { AuditLogService } from '../audit-log/audit-log.service';
import { ErrorCode } from '../common/error-code';
import { KolRegistrationDto, WholesaleRegistrationDto } from 'src/auth/auth.dto';
import { ReviewApplicationDto } from './registration-application.dto';

@Injectable()
export class RegistrationApplicationService {
  constructor(
    @InjectRepository(RegistrationApplication)
    private readonly applicationRepository: Repository<RegistrationApplication>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly auditLogService: AuditLogService,
  ) {}

  async createApplication(
    userId: string,
    type: RegistrationApplicationType,
    payload: KolRegistrationDto | WholesaleRegistrationDto,
    ip?: string,
    userAgent?: string,
  ): Promise<RegistrationApplication> {
    const existingApplication = await this.applicationRepository.findOne({
      where: { 
        userId, 
        type, 
        status: RegistrationApplicationStatus.PENDING 
      },
    });

    if (existingApplication) {
      throw new BadRequestException({
        errorCode: ErrorCode.VALIDATION_ERROR,
        message: 'Bạn đã có đơn đăng ký đang chờ xử lý',
      });
    }

    const application = this.applicationRepository.create({
      userId,
      type,
      payload,
      status: RegistrationApplicationStatus.PENDING,
    });

    const savedApplication = await this.applicationRepository.save(application);

    await this.auditLogService.logAction({
      userId,
      action: AuditAction.REGISTRATION_APPLICATION_CREATE,
      ip,
      userAgent,
      data: { type, applicationId: savedApplication.id },
    });

    return savedApplication;
  }

  async getApplicationsByUser(userId: string): Promise<RegistrationApplication[]> {
    return this.applicationRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  async getApplicationById(id: string): Promise<RegistrationApplication> {
    const application = await this.applicationRepository.findOne({
      where: { id },
      relations: ['user', 'reviewer'],
    });

    if (!application) {
      throw new BadRequestException({
        errorCode: ErrorCode.VALIDATION_ERROR,
        message: 'Đơn đăng ký không tồn tại',
      });
    }

    return application;
  }

  async getAllApplications(
    status?: RegistrationApplicationStatus,
    type?: RegistrationApplicationType,
    limit: number = 10,
    offset: number = 0,
  ): Promise<{ applications: RegistrationApplication[]; total: number }> {
    const queryBuilder = this.applicationRepository
      .createQueryBuilder('app')
      .leftJoinAndSelect('app.user', 'user')
      .leftJoinAndSelect('app.reviewer', 'reviewer')
      .orderBy('app.createdAt', 'DESC');

    if (status) {
      queryBuilder.andWhere('app.status = :status', { status });
    }

    if (type) {
      queryBuilder.andWhere('app.type = :type', { type });
    }

    const total = await queryBuilder.getCount();
    const applications = await queryBuilder.skip(offset).take(limit).getMany();

    return { applications, total };
  }

  async reviewApplication(
    applicationId: string,
    reviewDto: ReviewApplicationDto,
    reviewedBy: string,
    ip?: string,
    userAgent?: string,
  ): Promise<RegistrationApplication> {
    const application = await this.getApplicationById(applicationId);

    if (application.status !== RegistrationApplicationStatus.PENDING) {
      throw new BadRequestException({
        errorCode: ErrorCode.VALIDATION_ERROR,
        message: 'Chỉ có thể xét duyệt đơn đăng ký đang chờ xử lý',
      });
    }

    application.status = reviewDto.status;
    application.reviewedBy = reviewedBy;
    application.reviewedAt = new Date();
    if (reviewDto.note) {
      application.note = reviewDto.note;
    }

    const updatedApplication = await this.applicationRepository.save(application);

    if (reviewDto.status === RegistrationApplicationStatus.APPROVED) {
      const userType =
        application.type === RegistrationApplicationType.KOL
          ? UserType.KOL
          : UserType.WHOLESALE;

      await this.userRepository.update(application.userId, { type: userType });
    }

    await this.auditLogService.logAction({
      userId: application.userId,
      action: AuditAction.REGISTRATION_APPLICATION_REVIEW,
      ip,
      userAgent,
      data: {
        applicationId,
        status: reviewDto.status,
        reviewedBy,
        note: reviewDto.note,
        previousStatus: RegistrationApplicationStatus.PENDING,
      },
    });

    return updatedApplication;
  }
}
