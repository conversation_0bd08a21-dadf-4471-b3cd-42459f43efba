import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ResponseWrapperInterceptor } from './common/response-wrapper.interceptor';
import { ResponseExceptionFilter } from './common/response-exception.filter';
import { join } from 'path';
import { NestExpressApplication } from '@nestjs/platform-express';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  app.useGlobalInterceptors(new ResponseWrapperInterceptor());
  app.useGlobalFilters(new ResponseExceptionFilter());

  app.useStaticAssets(join(process.cwd(), 'public'));

  app.setGlobalPrefix(`sso/api`);

  app.enableCors({
    origin: [
      'http://localhost:3000',
      'https://merchandise.dev.dstmall.net',
      'https://dstmall.vn',
    ],
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE', // Specify allowed HTTP methods
  });

  const config = new DocumentBuilder()
    .setTitle('SSO API')
    .setDescription('API for SSO authentication')
    .setVersion('1.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'Authorization',
        description: 'Enter JWT token',
        in: 'header',
      },
      'access-token',
    )
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('sso/swagger', app, document);

  await app.listen(process.env.PORT ?? 3000);
}
bootstrap();
