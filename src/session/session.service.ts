import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull } from 'typeorm';
import { Session } from '../entities/session.entity';

@Injectable()
export class SessionService {
  constructor(
    @InjectRepository(Session)
    private readonly sessionRepository: Repository<Session>,
  ) {}

  async createSession(data: {
    userId: string;
    refreshToken: string;
    userAgent?: string;
    ip?: string;
  }): Promise<Session> {
    const session = this.sessionRepository.create(data);
    return this.sessionRepository.save(session);
  }

  async findByRefreshToken(refreshToken: string): Promise<Session | undefined> {
    const session = await this.sessionRepository.findOne({
      where: { refreshToken, revokedAt: IsNull() },
    });
    return session ?? undefined;
  }

  async revokeSession(refreshToken: string): Promise<void> {
    await this.sessionRepository.update(
      { refreshToken },
      { revokedAt: new Date() },
    );
  }
}
