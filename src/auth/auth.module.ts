import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { UserModule } from '../user/user.module';
import { PassportModule } from '@nestjs/passport';
import { JwtModule } from '@nestjs/jwt';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../entities/user.entity';
import { Session } from '../entities/session.entity';
import { Token } from '../entities/token.entity';
import { JwtStrategy } from './jwt.strategy';
import { SessionModule } from '../session/session.module';
import { AuditLogModule } from '../audit-log/audit-log.module';
import { AuditLog } from '../entities/audit-log.entity';
import { OtpModule } from '../otp/otp.module';
import { KeyStoreService } from '../common/key-store.service';

@Module({
  imports: [
    UserModule,
    PassportModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET,
      signOptions: { expiresIn: process.env.JWT_EXPIRES_IN },
    }),
    TypeOrmModule.forFeature([User, Session, Token, AuditLog]),
    SessionModule,
    AuditLogModule,
    OtpModule,
  ],
  providers: [AuthService, JwtStrategy, KeyStoreService],
  controllers: [AuthController],
})
export class AuthModule {}
