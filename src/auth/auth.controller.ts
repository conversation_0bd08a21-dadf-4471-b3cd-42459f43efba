import {
  Controller,
  Post,
  Body,
  Req,
  ValidationPipe,
  UsePipes,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import {
  RegisterDto,
  LoginDto,
  RefreshTokenDto,
  LogoutDto,
  ForgotPasswordDto,
  ResetPasswordDto,
} from './auth.dto';
import { Request } from 'express';
import {
  AuthTokens,
  AccessToken,
  LogoutResult,
  OtpResult,
} from '../common/types';
import { ApiBody } from '@nestjs/swagger';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('register')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async register(
    @Body() body: RegisterDto,
    @Req() req: Request,
  ): Promise<AuthTokens | OtpResult> {
    return this.authService.register(body, req.headers['user-agent'], req.ip);
  }

  @Post('login')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async login(
    @Body() body: LoginDto,
    @Req() req: Request,
  ): Promise<AuthTokens> {
    const audience = (req.headers['x-audience'] as string) || undefined;
    return this.authService.login(
      body.email ?? body.phone!,
      body.password,
      req.headers['user-agent'],
      req.ip,
      audience,
    );
  }

  @Post('refresh')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async refresh(@Body() body: RefreshTokenDto): Promise<AccessToken> {
    return this.authService.refreshToken(body.refreshToken);
  }

  @Post('logout')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async logout(@Body() body: LogoutDto): Promise<LogoutResult> {
    return this.authService.logout(body.refreshToken);
  }

  @Post('forgot-password')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  async forgotPassword(@Body() body: ForgotPasswordDto, @Req() req: Request) {
    const audience = (req.headers['x-audience'] as string) || undefined;
    return this.authService.forgotPassword(
      body.phone,
      req.ip,
      req.headers['user-agent'],
      audience,
    );
  }

  @Post('reset-password')
  @UsePipes(new ValidationPipe({ whitelist: true }))
  @ApiBody({ type: ResetPasswordDto })
  async resetPassword(@Body() body: ResetPasswordDto) {
    return this.authService.resetPassword(body.resetToken, body.newPassword);
  }
}
