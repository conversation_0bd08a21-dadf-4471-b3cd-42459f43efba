import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>otEmpty,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>If,
  <PERSON>,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class RegisterDto {
  @ApiPropertyOptional({ example: '<EMAIL>' })
  @ValidateIf((o: RegisterDto) => !o.phone)
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiPropertyOptional({ example: '+84901234567' })
  @ValidateIf((o: RegisterDto) => !o.email)
  @Matches(/^[+]?\d{9,15}$/)
  @IsOptional()
  phone?: string;

  @ApiProperty({ example: 'strongPassword123' })
  @MinLength(6)
  password: string;

  @ApiPropertyOptional({ example: '<PERSON>uyen <PERSON> A' })
  @IsOptional()
  name?: string;
}

export class LoginDto {
  @ApiPropertyOptional({ example: '<EMAIL>' })
  @ValidateIf((o: LoginDto) => !o.phone)
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiPropertyOptional({ example: '+84901234567' })
  @ValidateIf((o: LoginDto) => !o.email)
  @Matches(/^[+]?\d{9,15}$/)
  @IsOptional()
  phone?: string;

  @ApiProperty({ example: 'strongPassword123' })
  @MinLength(6)
  password: string;
}

export class RefreshTokenDto {
  @ApiProperty({ example: 'refresh_token_sample' })
  @IsNotEmpty()
  refreshToken: string;
}

export class LogoutDto {
  @ApiProperty({ example: 'refresh_token_sample' })
  @IsNotEmpty()
  refreshToken: string;
}

export class SendPhoneOtpDto {
  @ApiProperty({ example: '0901234567' })
  phone: string;
}

export class VerifyPhoneOtpDto {
  @ApiProperty({ example: '0901234567' })
  @IsNotEmpty()
  phone: string;

  @ApiProperty({ example: '123456' })
  @IsNotEmpty()
  otp: string;
}

export class ForgotPasswordDto {
  @ApiProperty({ example: '0901234567' })
  @IsNotEmpty()
  phone: string;
}

export class ResetPasswordDto {
  @ApiProperty({
    description: 'Reset token trả về sau khi verify OTP forgot_password',
  })
  @IsNotEmpty()
  resetToken: string;

  @ApiProperty({
    description: 'Mật khẩu mới',
  })
  @IsNotEmpty()
  @MinLength(6)
  newPassword: string;
}
