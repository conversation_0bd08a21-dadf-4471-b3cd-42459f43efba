import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UserService } from '../user/user.service';
import { SessionService } from '../session/session.service';
import { AuditLogService } from '../audit-log/audit-log.service';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';
import {
  AuthTokens,
  AccessToken,
  LogoutResult,
  OtpResult,
} from '../common/types';
import { plainToInstance } from 'class-transformer';
import { UserSafeDto } from '../common/user-safe.dto';
import { ErrorCode } from '../common/error-code';
import { OtpService } from '../otp/otp.service';
import { AuditAction } from '../common/audit-action.enum';
import { KeyStoreService } from '../common/key-store.service';

@Injectable()
export class AuthService {
  constructor(
    private readonly userService: UserService,
    private readonly jwtService: JwtService,
    private readonly sessionService: SessionService,
    private readonly auditLogService: AuditLogService,
    private readonly otpService: OtpService,
    private readonly keyStore: KeyStoreService,
  ) {}

  async validateUser(
    identifier: string,
    password: string,
  ): Promise<UserSafeDto | null> {
    const user = await this.userService.findByEmailOrPhone(identifier);
    if (user && (await bcrypt.compare(password, user.password))) {
      return plainToInstance(UserSafeDto, user, {
        excludeExtraneousValues: true,
      });
    }
    return null;
  }

  async login(
    identifier: string,
    password: string,
    userAgent?: string,
    ip?: string,
    audience?: string,
  ): Promise<AuthTokens> {
    const user = await this.userService.findByEmailOrPhone(identifier);
    if (!user) throw new UnauthorizedException('Invalid credentials');
    if (!identifier.includes('@')) {
      if (!user.isPhoneVerified) {
        throw new UnauthorizedException('Phone number is not verified');
      }
    }
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) throw new UnauthorizedException('Invalid credentials');
    const payload = {
      sub: user.id,
      phone: user.phone,
      email: user.email,
      aud: audience,
    } as {
      sub: string;
      phone: string;
      email: string;
      aud?: string;
    };

    const signing = this.keyStore.getSigningKeyForAudience(audience);

    const accessToken = signing?.privateKey
      ? (() => {
          const formattedKey = this.formatPemKey(signing.privateKey);
          try {
            crypto.createPrivateKey(formattedKey);
          } catch (keyError) {
            throw new Error(`Invalid private key: ${keyError.message}`);
          }

          return this.jwtService.sign(payload, {
            privateKey: formattedKey,
            algorithm: 'RS256',
            keyid: signing.kid,
            expiresIn: process.env.JWT_EXPIRES_IN,
          });
        })()
      : this.jwtService.sign(payload, {
          secret: process.env.JWT_SECRET as string,
          expiresIn: process.env.JWT_EXPIRES_IN,
        });

    const refreshToken = this.jwtService.sign(
      { sub: user.id, email: user.email },
      {
        secret: process.env.JWT_SECRET as string,
        expiresIn: process.env.REFRESH_TOKEN_EXPIRES_IN,
      },
    );

    await this.sessionService.createSession({
      userId: user.id,
      refreshToken,
      userAgent,
      ip,
    });
    await this.auditLogService.logAction({
      userId: user.id,
      action: AuditAction.LOGIN,
      ip,
      userAgent,
      data: { identifier, audience },
    });
    return { accessToken, refreshToken, userId: user.id };
  }

  formatPemKey(key: string): string {
    if (!key) return '';

    // First, convert literal \n strings to actual newlines
    const formattedKey = key.replace(/\\n/g, '\n');

    // Remove any existing headers/footers and clean up whitespace to get just the base64 content
    const cleanKey = formattedKey
      .replace(/-----BEGIN PRIVATE KEY-----/g, '')
      .replace(/-----END PRIVATE KEY-----/g, '')
      .replace(/\s+/g, '');

    // Validate that we have base64 content
    if (!cleanKey || cleanKey.length < 100) {
      throw new Error('Invalid private key: insufficient content');
    }

    // Split into 64-character lines
    const lines = cleanKey.match(/.{1,64}/g) || [];

    // Return properly formatted PEM key
    return `-----BEGIN PRIVATE KEY-----\n${lines.join('\n')}\n-----END PRIVATE KEY-----`;
  }

  async register(
    data: { email?: string; phone?: string; password: string; name?: string },
    userAgent?: string,
    ip?: string,
  ): Promise<AuthTokens | OtpResult> {
    if (!data.email && !data.phone) {
      throw new BadRequestException({
        errorCode: ErrorCode.VALIDATION_ERROR,
        message: 'Số điện thoại đã được đăng ký', // TODO:
      });
    }
    if (data.email) {
      const existed = await this.userService.findByEmail(data.email);
      if (existed)
        throw new BadRequestException({
          errorCode: ErrorCode.PHONE_EXISTS,
          message: 'Email đã được đăng ký', // TODO:
        });
    }
    if (data.phone) {
      const existed = await this.userService.findByPhone(data.phone);
      if (existed && existed.isPhoneVerified)
        throw new BadRequestException({
          errorCode: ErrorCode.PHONE_EXISTS,
          message: 'Số điện thoại đã được đăng ký',
        });
      if (existed && !existed.isPhoneVerified) {
        await this.userService.updateUser(existed.id, {
          password: data.password,
          name: data.name,
        });
        return await this.otpService.sendOtp(
          data.phone,
          'phone_verification',
          ip,
          userAgent,
        );
      }
    }
    // Tạo user mới
    const user = await this.userService.createUser({
      email: data.email,
      phone: data.phone,
      password: data.password,
      name: data.name,
    });
    if (data.phone) {
      return await this.otpService.sendOtp(
        data.phone,
        'phone_verification',
        ip,
        userAgent,
      );
    }
    await this.auditLogService.logAction({
      userId: user.id,
      action: AuditAction.REGISTER,
      ip,
      userAgent,
      data: { email: data.email, phone: data.phone },
    });
    return this.login(data.email ?? data.phone!, data.password, userAgent, ip);
  }

  async refreshToken(token: string): Promise<AccessToken> {
    try {
      const payload = this.jwtService.verify<{ sub: string; email: string }>(
        token,
        { secret: process.env.JWT_SECRET as string },
      );
      const session = await this.sessionService.findByRefreshToken(token);
      if (!session) throw new UnauthorizedException('Invalid refresh token');
      const accessToken = this.jwtService.sign(
        { sub: payload.sub, email: payload.email },
        {
          secret: process.env.JWT_SECRET as string,
          expiresIn: process.env.JWT_EXPIRES_IN,
        },
      );
      await this.auditLogService.logAction({
        userId: session.userId,
        action: AuditAction.REFRESH_TOKEN,
        ip: session.ip,
        userAgent: session.userAgent,
        data: {},
      });
      return { accessToken };
    } catch {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  async logout(refreshToken: string): Promise<LogoutResult> {
    const session = await this.sessionService.findByRefreshToken(refreshToken);
    await this.sessionService.revokeSession(refreshToken);
    await this.auditLogService.logAction({
      userId: session?.userId,
      action: AuditAction.LOGOUT,
      ip: session?.ip,
      userAgent: session?.userAgent,
      data: {},
    });
    return { message: 'Đăng xuất thành công' };
  }

  async forgotPassword(
    phone: string,
    ip?: string,
    userAgent?: string,
    audience?: string,
  ) {
    return this.otpService.sendOtp(
      phone,
      'forgot_password',
      ip,
      userAgent,
      audience,
    );
  }

  async resetPassword(resetToken: string, newPassword: string) {
    let payload;
    try {
      payload = this.jwtService.verify(resetToken, {
        secret: process.env.RESET_PASSWORD_SECRET,
      });
    } catch {
      throw new BadRequestException({
        errorCode: ErrorCode.INVALID_OTP,
        message: 'Mã xác thực không hợp lệ',
      });
    }

    const user = await this.userService.findById(payload.sub);
    if (!user) {
      throw new BadRequestException({
        errorCode: ErrorCode.USER_NOT_FOUND,
        message: 'Tài khoản không tồn tại',
      });
    }
    await this.userService.updateUser(user.id, { password: newPassword });
    return { message: 'Đổi mật khẩu thành công' };
  }
}
