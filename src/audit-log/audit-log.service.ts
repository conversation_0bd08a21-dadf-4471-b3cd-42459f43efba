import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AuditLog } from '../entities/audit-log.entity';
import { AuditAction } from '../common/audit-action.enum';

@Injectable()
export class AuditLogService {
  constructor(
    @InjectRepository(AuditLog)
    private readonly auditLogRepository: Repository<AuditLog>,
  ) {}

  async logAction(params: {
    userId?: string;
    clientId?: string;
    action: AuditAction;
    ip?: string;
    userAgent?: string;
    data?: any;
  }): Promise<AuditLog> {
    const log = this.auditLogRepository.create(params);
    return this.auditLogRepository.save(log);
  }

  async getLogs({
    userId,
    action,
    page = 1,
    limit = 20,
  }: {
    userId?: string;
    action?: string | AuditAction;
    page?: number;
    limit?: number;
  }) {
    const qb = this.auditLogRepository.createQueryBuilder('log');
    if (userId) qb.andWhere('log.userId = :userId', { userId });
    if (action) qb.andWhere('log.action = :action', { action });
    qb.orderBy('log.createdAt', 'DESC');
    qb.skip((page - 1) * limit).take(limit);
    const [items, total] = await qb.getManyAndCount();
    return { items, total, page, limit };
  }

  async deleteLog(id: string): Promise<void> {
    await this.auditLogRepository.delete(id);
  }
}
