import {
  Controller,
  Get,
  Query,
  Delete,
  Param,
  ParseUUIDPipe,
} from '@nestjs/common';
import { AuditLogService } from './audit-log.service';
import { ApiBearerAuth, ApiQuery, ApiTags } from '@nestjs/swagger';
import { UsePipes, ValidationPipe } from '@nestjs/common';

@ApiTags('audit-log')
@ApiBearerAuth('access-token')
@UsePipes(new ValidationPipe({ whitelist: true }))
@Controller('audit-logs')
export class AuditLogController {
  constructor(private readonly auditLogService: AuditLogService) {}

  @Get()
  @ApiQuery({ name: 'userId', required: false })
  @ApiQuery({ name: 'action', required: false })
  @ApiQuery({ name: 'page', required: false })
  @ApiQuery({ name: 'limit', required: false })
  async getLogs(
    @Query('userId') userId?: string,
    @Query('action') action?: string,
    @Query('page') page = 1,
    @Query('limit') limit = 20,
  ) {
    return this.auditLogService.getLogs({
      userId,
      action,
      page: +page,
      limit: +limit,
    });
  }

  @Delete(':id')
  async deleteLog(@Param('id', new ParseUUIDPipe()) id: string) {
    return this.auditLogService.deleteLog(id);
  }
}
