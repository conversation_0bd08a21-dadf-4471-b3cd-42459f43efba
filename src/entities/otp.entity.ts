import {
  En<PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
} from 'typeorm';

export type OtpType =
  | 'phone_verification'
  | 'email'
  | 'forgot_password'
  | 'change_password'
  | 'delete_account';

@Entity('otps')
export class Otp {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: true })
  userId?: string;

  @Column({ nullable: true })
  phone?: string;

  @Column()
  otp: string;

  @Column({ type: 'varchar' })
  type: OtpType;

  @Column({ type: 'timestamp' })
  expiresAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  usedAt?: Date;

  @CreateDateColumn()
  createdAt: Date;

  @Column({ nullable: true })
  ip?: string;

  @Column({ type: 'int', default: 0 })
  attempts: number;
}
