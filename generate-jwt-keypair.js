#!/usr/bin/env node

const crypto = require('crypto');
const fs = require('fs');

// Default values
const DEFAULT_AUDIENCE = 'dst-mall-be';
const DEFAULT_KID = 'key-dst-mall-2024';
const DEFAULT_KEY_SIZE = 2048;

// Parse command line arguments
function parseArgs() {
  const args = process.argv.slice(2);
  const config = {
    audience: DEFAULT_AUDIENCE,
    kid: DEFAULT_KID,
    keySize: DEFAULT_KEY_SIZE,
    outputFile: null,
    help: false
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    switch (arg) {
      case '--audience':
      case '-a':
        config.audience = args[++i];
        break;
      case '--kid':
      case '-k':
        config.kid = args[++i];
        break;
      case '--key-size':
      case '-s':
        config.keySize = parseInt(args[++i]);
        break;
      case '--output':
      case '-o':
        config.outputFile = args[++i];
        break;
      case '--help':
      case '-h':
        config.help = true;
        break;
      default:
        console.error(`Unknown argument: ${arg}`);
        process.exit(1);
    }
  }

  return config;
}

// Show help
function showHelp() {
  console.log(`
JWT Key Pair Generator
=====================

Usage: node generate-jwt-keypair.js [options]

Options:
  -a, --audience <audience>    Audience for the JWT (default: ${DEFAULT_AUDIENCE})
  -k, --kid <kid>             Key ID for the JWT (default: ${DEFAULT_KID})
  -s, --key-size <size>       RSA key size in bits (default: ${DEFAULT_KEY_SIZE})
  -o, --output <file>         Output configuration to file
  -h, --help                  Show this help message

Examples:
  # Generate with default values
  node generate-jwt-keypair.js

  # Generate for specific audience and kid
  node generate-jwt-keypair.js --audience "my-service" --kid "my-key-2024"

  # Generate and save to file
  node generate-jwt-keypair.js --output jwt-config.txt

  # Generate 4096-bit key
  node generate-jwt-keypair.js --key-size 4096
`);
}

// Generate key pair
function generateKeyPair(keySize) {
  console.log(`🔑 Generating ${keySize}-bit RSA key pair...`);
  
  const { publicKey, privateKey } = crypto.generateKeyPairSync('rsa', {
    modulusLength: keySize,
    publicKeyEncoding: {
      type: 'spki',
      format: 'pem'
    },
    privateKeyEncoding: {
      type: 'pkcs8',
      format: 'pem'
    }
  });

  return { publicKey, privateKey };
}

// Format keys for environment variables
function formatForEnvironment(audience, kid, publicKey, privateKey) {
  // Escape newlines for environment variables
  const escapedPrivateKey = privateKey.replace(/\n/g, '\\n');
  const escapedPublicKey = publicKey.replace(/\n/g, '\\n');

  // SSO service configuration (private key)
  const ssoConfig = `JWT_AUDIENCE_KEYS_JSON='{"${audience}":{"kid":"${kid}","privateKey":"${escapedPrivateKey}"}}'`;

  // Other services configuration (public key)
  const publicConfig = `SSO_JWT_PUBLIC_KEYS_JSON='{"${kid}":"${escapedPublicKey}"}'`;

  return { ssoConfig, publicConfig };
}

// Validate key pair
function validateKeyPair(publicKey, privateKey) {
  try {
    // Test signing and verification
    const testData = 'test-data-for-validation';
    const signature = crypto.sign('sha256', Buffer.from(testData), privateKey);
    const isValid = crypto.verify('sha256', Buffer.from(testData), publicKey, signature);
    
    if (!isValid) {
      throw new Error('Key pair validation failed');
    }

    console.log('✅ Key pair validation successful!');
    return true;
  } catch (error) {
    console.error('❌ Key pair validation failed:', error.message);
    return false;
  }
}

// Main function
function main() {
  const config = parseArgs();

  if (config.help) {
    showHelp();
    return;
  }

  console.log('JWT Key Pair Generator');
  console.log('====================');
  console.log(`Audience: ${config.audience}`);
  console.log(`Key ID: ${config.kid}`);
  console.log(`Key Size: ${config.keySize} bits`);
  console.log('');

  // Generate key pair
  const { publicKey, privateKey } = generateKeyPair(config.keySize);

  // Validate key pair
  if (!validateKeyPair(publicKey, privateKey)) {
    process.exit(1);
  }

  // Format for environment variables
  const { ssoConfig, publicConfig } = formatForEnvironment(
    config.audience,
    config.kid,
    publicKey,
    privateKey
  );

  // Output results
  const output = `
🔐 GENERATED JWT KEY PAIR
========================

📋 Private Key (Raw PEM):
${privateKey}

📋 Public Key (Raw PEM):
${publicKey}

🔧 SSO Service Environment Variable:
${ssoConfig}

🔧 Other Services Environment Variable:
${publicConfig}

📝 Usage Instructions:
1. Add the SSO environment variable to your SSO service
2. Add the public key environment variable to services that need to verify JWT tokens
3. Restart your services to pick up the new configuration

⚠️  Security Notes:
- Keep the private key secure and never expose it in client-side code
- The private key should only be used in the SSO service for signing tokens
- Public keys can be safely shared with services that need to verify tokens
- Consider rotating keys periodically for enhanced security
`;

  console.log(output);

  // Save to file if requested
  if (config.outputFile) {
    try {
      fs.writeFileSync(config.outputFile, output);
      console.log(`💾 Configuration saved to: ${config.outputFile}`);
    } catch (error) {
      console.error(`❌ Failed to save to file: ${error.message}`);
    }
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { generateKeyPair, formatForEnvironment, validateKeyPair };
